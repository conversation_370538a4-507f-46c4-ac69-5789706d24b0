/**
 * Examples of how to use the tRPC query optimization utilities
 *
 * This file demonstrates best practices for implementing optimized queries
 * in your tRPC application.
 */

import type { QueryClient } from "@tanstack/react-query";
import type { Job, Organization } from "@/db/schema";
import { infiniteQueryBuilders, infiniteQueryConfigs } from "./infinite-query";
import { parallelQueries } from "./parallel-queries";
import { queryKeys, queryOptionBuilders } from "./query-optimization";

// Type for tRPC client proxy - using a more generic approach
type TRPCClient = {
  [K in keyof any]: {
    [P in string]: {
      queryOptions: (...args: any[]) => any;
    };
  };
};

/**
 * Example: Using optimized query options for different data types
 */
export const queryExamples = {
  /**
   * Static data queries (trades, templates)
   * These rarely change, so we can cache them for longer
   */
  staticDataQueries: {
    // Example: Get all trades with static caching
    tradesQuery: (trpc: TRPCClient) =>
      queryOptionBuilders.static(trpc.trades.list.queryOptions()),

    // Example: Get job templates with static caching
    templatesQuery: (trpc: TRPCClient) =>
      queryOptionBuilders.static(trpc.templates.list.queryOptions()),
  },

  /**
   * User-specific data queries
   * These change based on user actions but not too frequently
   */
  userDataQueries: {
    // Example: Get user profile with user-specific caching
    userProfileQuery: (trpc: TRPCClient, userId: string) =>
      queryOptionBuilders.user(trpc.users.profile.queryOptions({ id: userId })),

    // Example: Get user's organization with user-specific caching
    userOrganizationQuery: (trpc: TRPCClient) =>
      queryOptionBuilders.user(trpc.contractor.getForUser.queryOptions()),
  },

  /**
   * Dynamic data queries
   * These change frequently and need regular updates
   */
  dynamicDataQueries: {
    // Example: Get jobs list with dynamic caching
    jobsListQuery: (trpc: TRPCClient, filters?: { limit?: number }) =>
      queryOptionBuilders.dynamic(trpc.jobs.list.queryOptions(filters)),

    // Example: Get bids for a job with dynamic caching
    jobBidsQuery: (trpc: TRPCClient, jobId: string) =>
      queryOptionBuilders.dynamic(trpc.bids.listByJob.queryOptions({ jobId })),
  },

  /**
   * Real-time data queries
   * These need to be as fresh as possible
   */
  realtimeDataQueries: {
    // Example: Get messages with real-time updates
    messagesQuery: (trpc: TRPCClient, { bidId, jobId }: { bidId?: string; jobId?: string }) =>
      queryOptionBuilders.realtime(trpc.messages.listMessages.queryOptions({ bidId, jobId })),

    // Example: Get notifications with real-time updates
    notificationsQuery: (trpc: TRPCClient) =>
      queryOptionBuilders.realtime(trpc.notifications.list.queryOptions()),
  },

  /**
   * Conditional queries
   * These only run when certain conditions are met
   */
  conditionalQueries: {
    // Example: Only fetch organization data for contractors
    organizationForContractorQuery: (trpc: TRPCClient, userRole: string) =>
      queryOptionBuilders.conditional(
        trpc.contractor.getForUser.queryOptions(),
        userRole === "contractor"
      ),

    // Example: Only fetch job details when job ID is available
    jobDetailsQuery: (trpc: TRPCClient, jobId?: string) =>
      queryOptionBuilders.conditional(
        trpc.jobs.getById.queryOptions({ id: jobId! }),
        !!jobId
      ),
  },

  /**
   * Dependent queries
   * These depend on data from other queries
   */
  dependentQueries: {
    // Example: Fetch job bids only after job is loaded
    jobWithBidsQuery: (trpc: TRPCClient, jobId: string, job: Job) =>
      queryOptionBuilders.dependent(
        trpc.bids.listByJob.queryOptions({ jobId }),
        job
      ),

    // Example: Fetch organization members only after organization is loaded
    organizationMembersQuery: (trpc: TRPCClient, organizationId: string, organization: Organization) =>
      queryOptionBuilders.dependent(
        trpc.contractor.getCrewMembers.queryOptions({ organizationId }),
        organization
      ),
  },
};

/**
 * Example: Using infinite queries for paginated data
 */
export const infiniteQueryExamples = {
  /**
   * Jobs list with cursor-based pagination
   */
  infiniteJobsList: (trpc: TRPCClient, filters?: { status?: string }) => {
    return infiniteQueryBuilders.cursor({
      ...trpc.jobs.listInfinite.queryOptions({
        limit: infiniteQueryConfigs.jobs.pageSize,
        ...filters
      }),
    });
  },

  /**
   * Messages list with real-time updates
   */
  infiniteMessagesList: (trpc: TRPCClient, chatId: string) => {
    return infiniteQueryBuilders.realtime({
      ...trpc.messages.listInfinite.queryOptions({
        chatId,
        limit: infiniteQueryConfigs.messages.pageSize,
      }),
    });
  },

  /**
   * Search results with cursor pagination
   */
  infiniteSearchResults: (trpc: TRPCClient, query: string) => {
    return infiniteQueryBuilders.cursor({
      ...trpc.search.infinite.queryOptions({
        query,
        limit: infiniteQueryConfigs.search.pageSize,
      }),
    });
  },
};

/**
 * Example: Using parallel queries for loading related data
 */
export const parallelQueryExamples = {
  /**
   * Load dashboard data in parallel
   */
  loadDashboardData: async (queryClient: QueryClient, trpc: TRPCClient, userId: string) => {
    return parallelQueries.executeParallel(queryClient, {
      user: () => queryClient.fetchQuery(trpc.users.profile.queryOptions({ id: userId })),
      jobs: () => queryClient.fetchQuery(trpc.jobs.list.queryOptions({ limit: 10 })),
      properties: () => queryClient.fetchQuery(trpc.properties.list.queryOptions()),
      notifications: () => queryClient.fetchQuery(trpc.notifications.list.queryOptions()),
    });
  },

  /**
   * Load job details with related data
   */
  loadJobWithRelatedData: async (queryClient: QueryClient, trpc: TRPCClient, jobId: string) => {
    return parallelQueries.executeParallel(queryClient, {
      job: () => queryClient.fetchQuery(trpc.jobs.getById.queryOptions({ id: jobId })),
      bids: () => queryClient.fetchQuery(trpc.bids.listByJob.queryOptions({ jobId })),
      messages: () => queryClient.fetchQuery(trpc.messages.listMessages.queryOptions({ jobId })),
      property: () => queryClient.fetchQuery(trpc.properties.one.queryOptions({ id: jobId })),
    });
  },

  /**
   * Prefetch related data for better UX
   */
  prefetchRelatedData: async (queryClient: QueryClient, trpc: TRPCClient, context: {
    userId?: string;
    organizationId?: string;
    propertyId?: string;
  }) => {
    const queries = [];

    if (context.userId) {
      queries.push({
        queryKey: queryKeys.userProfile(context.userId),
        queryFn: () => queryClient.fetchQuery(trpc.users.profile.queryOptions({ id: context.userId })),
      });
    }

    if (context.organizationId) {
      queries.push({
        queryKey: queryKeys.organizationMembers(context.organizationId),
        queryFn: () => queryClient.fetchQuery(trpc.contractor.getCrewMembers.queryOptions({
          organizationId: context.organizationId
        })),
      });
    }

    if (context.propertyId) {
      queries.push({
        queryKey: queryKeys.jobsByProperty(context.propertyId),
        queryFn: () => queryClient.fetchQuery(trpc.jobs.listByProperty.queryOptions({
          propertyId: context.propertyId
        })),
      });
    }

    return parallelQueries.prefetchParallel(queryClient, queries);
  },
};

/**
 * Example: Server-side prefetching patterns
 */
export const serverPrefetchExamples = {
  /**
   * Prefetch data for a job details page
   */
  prefetchJobPage: async (trpc: TRPCClient, jobId: string) => {
    // Using the enhanced prefetch utilities
    const { prefetchParallel, prefetchConditional } = await import("@/components/trpc/server");

    await prefetchParallel([
      trpc.jobs.getById.queryOptions({ id: jobId }),
      trpc.bids.listByJob.queryOptions({ jobId }),
      trpc.messages.listMessages.queryOptions({ jobId }),
    ]);
  },

  /**
   * Conditionally prefetch data based on user role
   */
  prefetchDashboardData: async (trpc: TRPCClient, userRole: string, userId: string) => {
    const { prefetchConditional } = await import("@/components/trpc/server");

    await prefetchConditional([
      {
        condition: userRole === "contractor",
        queryOptions: trpc.contractor.getForUser.queryOptions(),
      },
      {
        condition: userRole === "homeowner",
        queryOptions: trpc.properties.list.queryOptions(),
      },
      {
        condition: true, // Always prefetch user profile
        queryOptions: trpc.users.profile.queryOptions({ id: userId }),
      },
    ]);
  },

  /**
   * Smart prefetching that avoids refetching fresh data
   */
  smartPrefetchUserData: async (trpc: TRPCClient, userId: string) => {
    const { prefetchSmart } = await import("@/components/trpc/server");

    // Only prefetch if data is older than 1 minute
    await prefetchSmart(
      trpc.users.profile.queryOptions({ id: userId }),
      60000 // 1 minute
    );
  },
};

/**
 * Example: Cache invalidation patterns
 */
export const cacheInvalidationExamples = {
  /**
   * Invalidate related data when a job is updated
   */
  onJobUpdated: (queryClient: QueryClient, jobId: string, propertyId?: string) => {
    // Invalidate job-specific queries
    queryClient.invalidateQueries({ queryKey: queryKeys.job(jobId) });
    queryClient.invalidateQueries({ queryKey: queryKeys.jobBids(jobId) });

    // Invalidate general job lists
    queryClient.invalidateQueries({ queryKey: queryKeys.jobs() });

    // Invalidate property-specific job lists if property ID is available
    if (propertyId) {
      queryClient.invalidateQueries({ queryKey: queryKeys.jobsByProperty(propertyId) });
    }
  },

  /**
   * Invalidate related data when a bid is created/updated
   */
  onBidUpdated: (queryClient: QueryClient, bidId: string, jobId: string) => {
    // Invalidate bid-specific queries
    queryClient.invalidateQueries({ queryKey: queryKeys.bid(bidId) });

    // Invalidate job's bids list
    queryClient.invalidateQueries({ queryKey: queryKeys.jobBids(jobId) });

    // Invalidate general bids list
    queryClient.invalidateQueries({ queryKey: queryKeys.bids() });
  },

  /**
   * Invalidate message-related data when a new message is sent
   */
  onMessageSent: (queryClient: QueryClient, { bidId, jobId }: { bidId?: string; jobId?: string }) => {
    // Invalidate general messages
    queryClient.invalidateQueries({ queryKey: queryKeys.messages() });

    // Invalidate specific message lists
    if (bidId) {
      queryClient.invalidateQueries({ queryKey: queryKeys.messagesByBid(bidId) });
    }

    if (jobId) {
      queryClient.invalidateQueries({ queryKey: queryKeys.messagesByJob(jobId) });
    }
  },
};
