"use client";

import type { UserWithRole } from "better-auth/plugins";
import { useState } from "react";
import { toast } from "sonner";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { authClient } from "@/lib/auth-client";

interface UserActionsProps {
  user: UserWithRole;
  onUserUpdate?: () => void;
}

export function UserActions({ user, onUserUpdate }: UserActionsProps) {
  const [showBanDialog, setShowBanDialog] = useState(false);
  const [showRoleDialog, setShowRoleDialog] = useState(false);
  const [showImpersonateDialog, setShowImpersonateDialog] = useState(false);
  const [banReason, setBanReason] = useState("");
  const [selectedRole, setSelectedRole] = useState(user.role || "homeowner");
  const [isLoading, setIsLoading] = useState(false);

  const handleImpersonate = async () => {
    try {
      setIsLoading(true);
      
      // Use better-auth admin impersonation
      const result = await authClient.admin.impersonateUser({
        userId: user.id,
      });

      if (result.data) {
        toast.success(`Now impersonating ${user.name}`);
        // Redirect to user dashboard
        window.location.href = "/dashboard";
      } else {
        toast.error("Failed to impersonate user");
      }
    } catch (error) {
      console.error("Impersonation error:", error);
      toast.error("Failed to impersonate user");
    } finally {
      setIsLoading(false);
      setShowImpersonateDialog(false);
    }
  };

  const handleBanUser = async () => {
    try {
      setIsLoading(true);
      
      // Use better-auth admin ban functionality
      const result = await authClient.admin.banUser({
        userId: user.id,
        banReason: banReason || "Banned by administrator",
      });

      if (result.data) {
        toast.success(`User ${user.banned ? "unbanned" : "banned"} successfully`);
        onUserUpdate?.();
      } else {
        toast.error(`Failed to ${user.banned ? "unban" : "ban"} user`);
      }
    } catch (error) {
      console.error("Ban error:", error);
      toast.error(`Failed to ${user.banned ? "unban" : "ban"} user`);
    } finally {
      setIsLoading(false);
      setShowBanDialog(false);
      setBanReason("");
    }
  };

  const handleUnbanUser = async () => {
    try {
      setIsLoading(true);
      
      const result = await authClient.admin.unbanUser({
        userId: user.id,
      });

      if (result.data) {
        toast.success("User unbanned successfully");
        onUserUpdate?.();
      } else {
        toast.error("Failed to unban user");
      }
    } catch (error) {
      console.error("Unban error:", error);
      toast.error("Failed to unban user");
    } finally {
      setIsLoading(false);
    }
  };

  const handleRoleChange = async () => {
    try {
      setIsLoading(true);
      
      const result = await authClient.admin.setRole({
        userId: user.id,
        role: selectedRole,
      });

      if (result.data) {
        toast.success(`User role updated to ${selectedRole}`);
        onUserUpdate?.();
      } else {
        toast.error("Failed to update user role");
      }
    } catch (error) {
      console.error("Role change error:", error);
      toast.error("Failed to update user role");
    } finally {
      setIsLoading(false);
      setShowRoleDialog(false);
    }
  };

  return (
    <>
      {/* Impersonate Confirmation Dialog */}
      <AlertDialog open={showImpersonateDialog} onOpenChange={setShowImpersonateDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Impersonate User</AlertDialogTitle>
            <AlertDialogDescription>
              You are about to impersonate <strong>{user.name}</strong> ({user.email}).
              You will be logged in as this user and redirected to their dashboard.
              You can return to your admin account at any time.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleImpersonate}
              disabled={isLoading}
            >
              {isLoading ? "Impersonating..." : "Impersonate"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Ban/Unban Dialog */}
      <AlertDialog open={showBanDialog} onOpenChange={setShowBanDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {user.banned ? "Unban User" : "Ban User"}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {user.banned ? (
                <>
                  You are about to unban <strong>{user.name}</strong>. 
                  They will regain access to their account immediately.
                </>
              ) : (
                <>
                  You are about to ban <strong>{user.name}</strong>. 
                  They will lose access to their account immediately.
                </>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          
          {!user.banned && (
            <div className="space-y-2">
              <Label htmlFor="banReason">Ban Reason (Optional)</Label>
              <Textarea
                id="banReason"
                placeholder="Enter reason for banning this user..."
                value={banReason}
                onChange={(e) => setBanReason(e.target.value)}
              />
            </div>
          )}
          
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={user.banned ? handleUnbanUser : handleBanUser}
              disabled={isLoading}
              className={user.banned ? "" : "bg-red-600 hover:bg-red-700"}
            >
              {isLoading 
                ? (user.banned ? "Unbanning..." : "Banning...") 
                : (user.banned ? "Unban User" : "Ban User")
              }
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Role Change Dialog */}
      <Dialog open={showRoleDialog} onOpenChange={setShowRoleDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Change User Role</DialogTitle>
            <DialogDescription>
              Update the role for <strong>{user.name}</strong>. 
              This will change their permissions and access level.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="role">Select New Role</Label>
              <Select value={selectedRole} onValueChange={setSelectedRole}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="homeowner">Homeowner</SelectItem>
                  <SelectItem value="contractor">Contractor</SelectItem>
                  <SelectItem value="admin">Administrator</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="rounded-lg bg-muted p-3">
              <p className="text-muted-foreground text-sm">
                <strong>Current Role:</strong> {user.role?.charAt(0).toUpperCase() + user.role?.slice(1)}
              </p>
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowRoleDialog(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleRoleChange}
              disabled={isLoading || selectedRole === user.role}
            >
              {isLoading ? "Updating..." : "Update Role"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Action Buttons */}
      <div className="flex gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setShowImpersonateDialog(true)}
        >
          Impersonate
        </Button>
        
        <Button
          variant="outline"
          size="sm"
          onClick={() => setShowRoleDialog(true)}
        >
          Change Role
        </Button>
        
        <Button
          variant={user.banned ? "default" : "destructive"}
          size="sm"
          onClick={() => setShowBanDialog(true)}
        >
          {user.banned ? "Unban" : "Ban"}
        </Button>
      </div>
    </>
  );
}

// Export individual action components for use in other places
export function ImpersonateButton({ user }: { user: UserWithRole }) {
  const [showDialog, setShowDialog] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleImpersonate = async () => {
    try {
      setIsLoading(true);
      const result = await authClient.admin.impersonateUser({
        userId: user.id,
      });

      if (result.data) {
        toast.success(`Now impersonating ${user.name}`);
        window.location.href = "/dashboard";
      } else {
        toast.error("Failed to impersonate user");
      }
    } catch (error) {
      console.error("Impersonation error:", error);
      toast.error("Failed to impersonate user");
    } finally {
      setIsLoading(false);
      setShowDialog(false);
    }
  };

  return (
    <>
      <Button
        variant="outline"
        size="sm"
        onClick={() => setShowDialog(true)}
      >
        Impersonate
      </Button>

      <AlertDialog open={showDialog} onOpenChange={setShowDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Impersonate User</AlertDialogTitle>
            <AlertDialogDescription>
              You are about to impersonate <strong>{user.name}</strong> ({user.email}).
              You will be logged in as this user and redirected to their dashboard.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleImpersonate}
              disabled={isLoading}
            >
              {isLoading ? "Impersonating..." : "Impersonate"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
