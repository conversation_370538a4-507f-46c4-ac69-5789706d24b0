import { <PERSON><PERSON>ef<PERSON> } from "lucide-react";
import Link from "next/link";
import type { ReactNode } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";

export interface PageAction {
  label: string;
  href?: string;
  onClick?: () => void;
  variant?:
    | "default"
    | "destructive"
    | "outline"
    | "secondary"
    | "ghost"
    | "link"
    | "tc_blue"
    | "tc_orange";
  size?: "default" | "sm" | "lg" | "icon";
  icon?: ReactNode;
  disabled?: boolean;
}

export interface PageBreadcrumb {
  label: string;
  href?: string;
}

export interface PageHeaderProps {
  title: string;
  description?: string;
  badge?: {
    label: string;
    variant?: "default" | "secondary" | "destructive" | "outline";
  };
  breadcrumbs?: PageBreadcrumb[];
  backButton?: {
    href: string;
    label?: string;
  };
  actions?: PageAction[];
  primaryAction?: PageAction;
  className?: string;
}

export function PageHeader({
  title,
  description,
  badge,
  breadcrumbs = [],
  backButton,
  actions = [],
  primaryAction,
  className,
}: PageHeaderProps) {
  return (
    <div className={cn("space-y-4", className)}>
      {/* Breadcrumbs */}
      {breadcrumbs.length > 0 && (
        <nav className="flex items-center space-x-1 text-muted-foreground text-sm">
          {breadcrumbs.map((crumb, index) => (
            <div
              key={`breadcrumb-${crumb.label}`}
              className="flex items-center"
            >
              {index > 0 && <span className="mx-2">/</span>}
              {crumb.href ? (
                <Link
                  href={crumb.href}
                  className="transition-colors hover:text-foreground"
                >
                  {crumb.label}
                </Link>
              ) : (
                <span className="text-foreground">{crumb.label}</span>
              )}
            </div>
          ))}
        </nav>
      )}

      {/* Header content */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="space-y-2">
          {/* Back button */}
          {backButton && (
            <Button variant="ghost" size="sm" asChild className="-ml-2 mb-2">
              <Link href={backButton.href}>
                <ArrowLeft className="mr-2 h-4 w-4" />
                {backButton.label || "Back"}
              </Link>
            </Button>
          )}

          {/* Title and badge */}
          <div className="flex items-center gap-3">
            <h1 className="font-bold text-3xl tracking-tight">{title}</h1>
            {badge && <Badge variant={badge.variant}>{badge.label}</Badge>}
          </div>

          {/* Description */}
          {description && (
            <p className="text-lg text-muted-foreground">{description}</p>
          )}
        </div>

        {/* Actions */}
        {(actions.length > 0 || primaryAction) && (
          <div className="flex items-center gap-2">
            {actions.map((action) => (
              <ActionButton key={`action-${action.label}`} action={action} />
            ))}
            {primaryAction && <ActionButton action={primaryAction} isPrimary />}
          </div>
        )}
      </div>

      <Separator />
    </div>
  );
}

export interface DetailPageLayoutProps {
  header: PageHeaderProps;
  sidebar?: ReactNode;
  children: ReactNode;
  sidebarPosition?: "left" | "right";
  sidebarWidth?: "sm" | "md" | "lg";
  className?: string;
}

export function DetailPageLayout({
  header,
  sidebar,
  children,
  sidebarPosition = "right",
  sidebarWidth = "md",
  className,
}: DetailPageLayoutProps) {
  const sidebarWidthClasses = {
    sm: "md:w-64",
    md: "md:w-80",
    lg: "md:w-96",
  };

  return (
    <div className={cn("container mx-auto space-y-6 p-6", className)}>
      <PageHeader {...header} />

      <div
        className={cn(
          "grid gap-6",
          sidebar ? "md:grid-cols-[1fr_auto]" : "grid-cols-1",
          sidebarPosition === "left" && "md:grid-cols-[auto_1fr]",
        )}
      >
        {/* Sidebar - Left */}
        {sidebar && sidebarPosition === "left" && (
          <aside className={cn("space-y-6", sidebarWidthClasses[sidebarWidth])}>
            {sidebar}
          </aside>
        )}

        {/* Main content */}
        <main className="min-w-0">{children}</main>

        {/* Sidebar - Right */}
        {sidebar && sidebarPosition === "right" && (
          <aside className={cn("space-y-6", sidebarWidthClasses[sidebarWidth])}>
            {sidebar}
          </aside>
        )}
      </div>
    </div>
  );
}

export interface ListPageLayoutProps {
  header: PageHeaderProps;
  filters?: ReactNode;
  children: ReactNode;
  className?: string;
}

export function ListPageLayout({
  header,
  filters,
  children,
  className,
}: ListPageLayoutProps) {
  return (
    <div className={cn("container mx-auto space-y-6 p-6", className)}>
      <PageHeader {...header} />

      {filters && (
        <Card>
          <CardContent className="pt-6">{filters}</CardContent>
        </Card>
      )}

      <div>{children}</div>
    </div>
  );
}

export interface FormPageLayoutProps {
  header: PageHeaderProps;
  children: ReactNode;
  maxWidth?: "sm" | "md" | "lg" | "xl" | "full";
  variant?: "default" | "card";
  className?: string;
}

export function FormPageLayout({
  header,
  children,
  maxWidth = "lg",
  variant = "default",
  className,
}: FormPageLayoutProps) {
  const maxWidthClasses = {
    sm: "max-w-sm",
    md: "max-w-md",
    lg: "max-w-lg",
    xl: "max-w-xl",
    full: "max-w-full",
  };

  return (
    <div className={cn("container mx-auto space-y-6 p-6", className)}>
      <PageHeader {...header} />

      <div className={cn("mx-auto", maxWidthClasses[maxWidth])}>
        {variant === "card" ? (
          <Card>
            <CardContent className="pt-6">{children}</CardContent>
          </Card>
        ) : (
          children
        )}
      </div>
    </div>
  );
}

export interface DashboardLayoutProps {
  header: PageHeaderProps;
  stats?: ReactNode;
  quickActions?: ReactNode;
  children: ReactNode;
  className?: string;
}

export function DashboardLayout({
  header,
  stats,
  quickActions,
  children,
  className,
}: DashboardLayoutProps) {
  return (
    <div className={cn("container mx-auto space-y-6 p-6", className)}>
      <PageHeader {...header} />

      {/* Stats section */}
      {stats && <section>{stats}</section>}

      {/* Quick actions */}
      {quickActions && (
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>Common tasks and shortcuts</CardDescription>
          </CardHeader>
          <CardContent>{quickActions}</CardContent>
        </Card>
      )}

      {/* Main content */}
      <section>{children}</section>
    </div>
  );
}

function ActionButton({
  action,
  isPrimary = false,
}: {
  action: PageAction;
  isPrimary?: boolean;
}) {
  const buttonProps = {
    variant: action.variant || (isPrimary ? "tc_blue" : "outline"),
    size: action.size || "default",
    disabled: action.disabled,
    onClick: action.onClick,
  };

  const content = (
    <>
      {action.icon && <span className="mr-2">{action.icon}</span>}
      {action.label}
    </>
  );

  if (action.href && !action.onClick) {
    return (
      <Button asChild {...buttonProps}>
        <Link href={action.href}>{content}</Link>
      </Button>
    );
  }

  return <Button {...buttonProps}>{content}</Button>;
}
